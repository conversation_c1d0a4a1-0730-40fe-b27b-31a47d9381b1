import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:url_launcher/url_launcher.dart';
import '../models/subject.dart';
import '../models/pdf_model.dart';
import '../theme/app_theme.dart';
import '../utils/pdf_url_tester.dart';

class PDFViewerScreen extends StatefulWidget {
  final Subject subject;
  final String? pdfFileName;
  final String? category;
  final String? pdfUrl; // رابط PDF الحقيقي
  final PDFModel? pdfModel; // نموذج PDF الكامل

  const PDFViewerScreen({
    super.key,
    required this.subject,
    this.pdfFileName,
    this.category,
    this.pdfUrl,
    this.pdfModel,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen>
    with TickerProviderStateMixin {
  PDFViewController? _pdfViewController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  bool _isLoading = true;
  bool _showControls = true;
  bool _hasError = false;
  int _currentPage = 1;
  int _totalPages = 0;
  String? _localPdfPath;
  double _downloadProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _initializePdfViewer();

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );

    _fabAnimationController.forward();
  }

  void _initializePdfViewer() async {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;

    if (kDebugMode) {
      print('🔍 تهيئة عارض PDF:');
      print('📄 widget.pdfUrl: ${widget.pdfUrl}');
      print('📄 widget.pdfModel?.url: ${widget.pdfModel?.url}');
      print('📄 Final URL: $pdfUrl');
    }

    if (pdfUrl != null && pdfUrl.isNotEmpty) {
      await _downloadAndCachePdf(pdfUrl);
    } else {
      if (kDebugMode) {
        print('❌ لا يوجد رابط PDF صالح');
      }
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  /// الحصول على ملف مخزن مؤقتاً
  Future<File?> _getCachedFile(String url) async {
    try {
      final directory = await getTemporaryDirectory();
      final fileName = '${url.hashCode}.pdf';
      final file = File('${directory.path}/$fileName');
      return file;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على الملف المؤقت: $e');
      }
      return null;
    }
  }

  /// تحسين استخدام الذاكرة
  void _optimizeMemory() {
    // تنظيف الذاكرة المؤقتة
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        // تنظيف المتغيرات غير المستخدمة
        setState(() {
          // إعادة تعيين متغيرات التحميل
          _downloadProgress = 1.0;
        });
      }
    });
  }

  /// تحميل PDF محسن للأندرويد مع عرض فوري
  Future<void> _downloadAndCachePdf(String url) async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _downloadProgress = 0.0;
      });

      if (kDebugMode) {
        print('🔄 بدء تحميل PDF: $url');
      }

      // تحويل رابط Google Drive إلى رابط تحميل مباشر إن لزم الأمر
      final String directUrl = _convertToDirectDownloadUrl(url);

      if (kDebugMode) {
        print('🔗 الرابط المباشر: $directUrl');
      }

      // التحقق من وجود الملف في التخزين المؤقت أولاً
      final cachedFile = await _getCachedFile(directUrl);
      if (cachedFile != null && await cachedFile.exists()) {
        setState(() {
          _localPdfPath = cachedFile.path;
          _isLoading = false;
          _downloadProgress = 1.0;
        });
        if (kDebugMode) {
          print('✅ تم العثور على PDF في التخزين المؤقت');
        }
        return;
      }

      // تحميل تدريجي - تحميل أول جزء للعرض السريع
      final client = http.Client();

      // تحميل كامل مع تحسينات
      final request = http.Request('GET', Uri.parse(directUrl));
      request.headers.addAll({
        'User-Agent': 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36',
        'Accept': 'application/pdf,*/*',
        'Connection': 'keep-alive',
        'Accept-Encoding': 'identity', // تجنب الضغط
      });

      final streamedResponse = await client
          .send(request)
          .timeout(const Duration(seconds: 30));

      final response = await http.Response.fromStream(streamedResponse);

      if (kDebugMode) {
        print('📊 حالة الاستجابة: ${response.statusCode}');
        print('📊 نوع المحتوى: ${response.headers['content-type']}');
        print('📊 حجم البيانات: ${response.bodyBytes.length} بايت');
        print('📊 جميع Headers: ${response.headers}');
        if (response.bodyBytes.isNotEmpty) {
          print('📊 أول 100 بايت: ${response.bodyBytes.take(100).toList()}');
        }
      }

      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;

        // التحقق من أن البيانات ليست فارغة
        if (bytes.isEmpty) {
          throw Exception('الملف المحمل فارغ');
        }

        // التحقق من أن البيانات تحتوي على PDF
        if (!_isPdfData(bytes)) {
          if (kDebugMode) {
            print('⚠️ البيانات المحملة قد لا تكون PDF صحيح');
            print('⚠️ أول 50 بايت: ${bytes.take(50).toList()}');
          }
        }

        final dir = await getTemporaryDirectory();
        final fileName =
            'temp_pdf_${DateTime.now().millisecondsSinceEpoch}.pdf';
        final file = File('${dir.path}/$fileName');

        await file.writeAsBytes(bytes);

        // التحقق من أن الملف تم إنشاؤه بنجاح
        if (await file.exists()) {
          setState(() {
            _localPdfPath = file.path;
            _isLoading = false;
            _downloadProgress = 1.0;
          });

          if (kDebugMode) {
            print('✅ تم تحميل PDF بنجاح: ${file.path}');
            print('✅ حجم الملف: ${await file.length()} بايت');
          }
        } else {
          throw Exception('فشل في إنشاء الملف المحلي');
        }
      } else if (response.statusCode == 302 || response.statusCode == 301) {
        // إعادة توجيه - نحاول الرابط الجديد
        final location = response.headers['location'];
        if (location != null) {
          if (kDebugMode) {
            print('🔄 إعادة توجيه إلى: $location');
          }
          await _downloadAndCachePdf(location);
          return;
        }
        throw Exception('إعادة توجيه بدون رابط جديد');
      } else {
        throw Exception(
          'فشل في تحميل PDF: ${response.statusCode} - ${response.reasonPhrase}',
        );
      }
    } catch (e) {
      String errorMessage = 'خطأ غير معروف';
      String technicalDetails = '';

      if (e.toString().contains('SocketException')) {
        errorMessage = 'لا يوجد اتصال بالإنترنت';
        technicalDetails = 'تحقق من اتصال الإنترنت';
      } else if (e.toString().contains('TimeoutException')) {
        errorMessage = 'انتهت مهلة التحميل';
        technicalDetails = 'الملف كبير أو الاتصال بطيء';
      } else if (e.toString().contains('HttpException')) {
        errorMessage = 'خطأ في الخادم';
        technicalDetails = 'مشكلة في الرابط أو الخادم';
      } else if (e.toString().contains('FormatException')) {
        errorMessage = 'رابط غير صحيح';
        technicalDetails = 'تحقق من صحة الرابط';
      } else {
        errorMessage = 'فشل في تحميل الملف';
        technicalDetails = e.toString();
      }

      if (kDebugMode) {
        print('❌ خطأ في تحميل PDF: $e');
        print('❌ نوع الخطأ: ${e.runtimeType}');
        print('❌ رسالة الخطأ: $errorMessage');
        print('❌ التفاصيل التقنية: $technicalDetails');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });

        // عرض رسالة خطأ مفصلة للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  errorMessage,
                  style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                ),
                if (technicalDetails.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    technicalDetails,
                    style: GoogleFonts.cairo(fontSize: 12),
                  ),
                ],
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: _retryDownload,
            ),
          ),
        );
      }
    }
  }

  /// التحقق من أن البيانات تحتوي على PDF صحيح
  bool _isPdfData(List<int> bytes) {
    if (bytes.length < 4) return false;
    // PDF files start with %PDF
    return bytes[0] == 0x25 && // %
        bytes[1] == 0x50 && // P
        bytes[2] == 0x44 && // D
        bytes[3] == 0x46; // F
  }

  /// تحويل روابط Google Drive إلى روابط تحميل مباشرة
  String _convertToDirectDownloadUrl(String url) {
    if (kDebugMode) {
      print('🔗 معالجة الرابط: $url');
    }

    // إذا كان رابط Google Drive
    if (url.contains('drive.google.com')) {
      // نمط 1: /file/d/FILE_ID/view أو /file/d/FILE_ID/edit
      RegExp regex = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
      RegExpMatch? match = regex.firstMatch(url);

      if (match != null) {
        final fileId = match.group(1);
        // جرب عدة طرق للتحميل
        final directUrl =
            'https://drive.google.com/uc?export=download&id=$fileId&confirm=t';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive: $fileId -> $directUrl');
        }
        return directUrl;
      }

      // نمط 2: إذا كان يحتوي على id= بالفعل
      regex = RegExp(r'[?&]id=([a-zA-Z0-9-_]+)');
      match = regex.firstMatch(url);
      if (match != null) {
        final fileId = match.group(1);
        final directUrl =
            'https://drive.google.com/uc?export=download&id=$fileId&confirm=t';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive (ID): $fileId -> $directUrl');
        }
        return directUrl;
      }

      // نمط 3: رابط مشاركة مع usp=sharing
      regex = RegExp(r'drive\.google\.com/file/d/([a-zA-Z0-9-_]+)/.*');
      match = regex.firstMatch(url);
      if (match != null) {
        final fileId = match.group(1);
        final directUrl =
            'https://drive.google.com/uc?export=download&id=$fileId&confirm=t';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive (مشاركة): $fileId -> $directUrl');
        }
        return directUrl;
      }
    }

    // إذا كان رابط Dropbox
    if (url.contains('dropbox.com') && url.contains('dl=0')) {
      final directUrl = url.replaceAll('dl=0', 'dl=1');
      if (kDebugMode) {
        print('🔄 تحويل Dropbox: $directUrl');
      }
      return directUrl;
    }

    // إذا كان رابط OneDrive
    if (url.contains('1drv.ms') || url.contains('onedrive.live.com')) {
      if (url.contains('?')) {
        final directUrl = '$url&download=1';
        if (kDebugMode) {
          print('🔄 تحويل OneDrive: $directUrl');
        }
        return directUrl;
      }
    }

    // إذا كان رابط مباشر، نعيده كما هو
    if (kDebugMode) {
      print('✅ رابط مباشر: $url');
    }
    return url;
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    // تنظيف الملف المؤقت
    _cleanupTempFile();
    super.dispose();
  }

  /// تنظيف الملف المؤقت
  void _cleanupTempFile() async {
    if (_localPdfPath != null) {
      try {
        final file = File(_localPdfPath!);
        if (await file.exists()) {
          await file.delete();
          if (kDebugMode) {
            print('🗑️ تم حذف الملف المؤقت: $_localPdfPath');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حذف الملف المؤقت: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // الهيدر
            _buildHeader(),

            // عارض PDF
            Expanded(
              child: Stack(
                children: [
                  // PDF Viewer
                  Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: AppTheme.cardShadow,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _buildPdfViewer(),
                    ),
                  ),

                  // مؤشر التحميل
                  if (_isLoading)
                    Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),

                  // أدوات التحكم العائمة
                  if (_showControls && !_isLoading) _buildFloatingControls(),
                ],
              ),
            ),

            // شريط المعلومات السفلي
            if (!_isLoading) _buildBottomInfo(),
          ],
        ),
      ),
    );
  }

  /// بناء عارض PDF المناسب
  Widget _buildPdfViewer() {
    if (kDebugMode) {
      print('🔍 PDF Viewer Debug:');
      print('📄 localPdfPath: $_localPdfPath');
      print('📄 isLoading: $_isLoading');
      print('📄 hasError: $_hasError');
    }

    // إذا كان هناك خطأ
    if (_hasError) {
      return _buildErrorView('فشل في تحميل الملف');
    }

    // إذا لم يتم تحميل الملف بعد
    if (_localPdfPath == null) {
      return _buildLoadingView();
    }

    // عرض PDF من الملف المحلي
    return _buildLocalPdfViewer(_localPdfPath!);
  }

  /// عرض شاشة التحميل مع شريط التقدم
  Widget _buildLoadingView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.download_rounded, size: 80, color: AppTheme.primaryColor),
        const SizedBox(height: 24),
        Text(
          'جاري تحميل الملف...',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: 200,
          child: LinearProgressIndicator(
            value: _downloadProgress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${(_downloadProgress * 100).toInt()}%',
          style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
        ),
      ],
    );
  }

  /// عارض PDF من الملف المحلي باستخدام flutter_pdfview
  Widget _buildLocalPdfViewer(String filePath) {
    if (kDebugMode) {
      print('📄 عرض PDF من الملف المحلي: $filePath');
      print('🌐 هل هو ويب؟ $kIsWeb');
    }

    // إذا كان على الويب، استخدم عارض بديل دائماً
    if (kIsWeb) {
      if (kDebugMode) {
        print('🌐 تشغيل على الويب - استخدام عارض بديل');
      }
      return _buildWebPdfViewer();
    }

    // للموبايل فقط - استخدم flutter_pdfview محسن
    try {
      return PDFView(
        filePath: filePath,
        enableSwipe: true,
        swipeHorizontal: false,
        autoSpacing: false, // تحسين الأداء
        pageFling: false, // تحسين الأداء
        pageSnap: true,
        defaultPage: 0,
        fitPolicy: FitPolicy.WIDTH, // تحسين العرض
        preventLinkNavigation: false,
        nightMode: false, // تحسين الأداء
        onRender: (pages) {
          setState(() {
            _totalPages = pages ?? 0;
            _isLoading = false;
          });
          if (kDebugMode) {
            print('✅ تم تحميل PDF بنجاح: $pages صفحة');
          }
          // تحسين الذاكرة بعد التحميل
          _optimizeMemory();
        },
        onError: (error) {
          setState(() {
            _isLoading = false;
            _hasError = true;
          });
          if (kDebugMode) {
            print('❌ خطأ في عرض PDF: $error');
          }
        },
        onPageError: (page, error) {
          if (kDebugMode) {
            print('❌ خطأ في الصفحة $page: $error');
          }
        },
        onViewCreated: (PDFViewController pdfViewController) {
          _pdfViewController = pdfViewController;
        },
        onLinkHandler: (uri) {
          if (kDebugMode) {
            print('🔗 رابط: $uri');
          }
        },
        onPageChanged: (page, total) {
          setState(() {
            _currentPage = page! + 1; // flutter_pdfview يبدأ من 0
          });
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء PDFView: $e');
      }
      // في حالة فشل PDFView، استخدم العارض البديل
      return _buildWebPdfViewer();
    }
  }

  /// عارض PDF للويب محسن
  Widget _buildWebPdfViewer() {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;
    if (pdfUrl == null || pdfUrl.isEmpty) {
      return _buildErrorView('رابط PDF غير صالح');
    }

    // تحويل إلى رابط مباشر
    final String directUrl = _convertToDirectDownloadUrl(pdfUrl);

    if (kIsWeb) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: HtmlElementView(
            viewType: 'pdf-viewer-${DateTime.now().millisecondsSinceEpoch}',
            onPlatformViewCreated: (id) {
              // تم إزالة هذا للأندرويد
            },
          ),
        ),
      );
    }

    // للمنصات الأخرى - عرض بديل
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.picture_as_pdf_rounded,
          size: 80,
          color: Colors.red.shade400,
        ),
        const SizedBox(height: 24),
        Text(
          'عارض PDF - الويب',
          style: GoogleFonts.cairo(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            children: [
              Text(
                widget.pdfModel?.name ?? 'ملف PDF',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue.shade800,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'flutter_pdfview لا يعمل على الويب.\nاختر طريقة العرض المناسبة:',
                style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),

        // خيارات متعددة للعرض
        Wrap(
          spacing: 16,
          runSpacing: 16,
          alignment: WrapAlignment.center,
          children: [
            // فتح في نافذة جديدة
            ElevatedButton.icon(
              onPressed: () => _openInNewTab(directUrl),
              icon: const Icon(Icons.open_in_new, color: Colors.white),
              label: Text(
                'فتح في نافذة جديدة',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 4,
              ),
            ),

            // تحميل الملف
            OutlinedButton.icon(
              onPressed: () => _downloadFile(directUrl),
              icon: Icon(Icons.download, color: Colors.green.shade600),
              label: Text(
                'تحميل الملف',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green.shade600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: BorderSide(color: Colors.green.shade600),
              ),
            ),

            // عرض الرابط المباشر
            TextButton.icon(
              onPressed: () => _showDirectUrl(directUrl),
              icon: Icon(Icons.link, color: Colors.orange.shade600),
              label: Text(
                'عرض الرابط المباشر',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.orange.shade600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// فتح الرابط في نافذة جديدة
  void _openInNewTab(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (kDebugMode) {
          print('❌ لا يمكن فتح الرابط: $url');
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن فتح الرابط', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فتح الرابط: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الرابط: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إعادة تحميل PDF
  void _retryDownload() async {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;
    if (pdfUrl != null && pdfUrl.isNotEmpty) {
      await _downloadAndCachePdf(pdfUrl);
    }
  }

  /// اختبار رابط محدد (للتشخيص)
  Future<void> _testSpecificUrl() async {
    // اختبار الرابط المحدد من المستخدم
    await PDFUrlTester.testUserUrl();

    // اختبار الرابط الحالي أيضاً
    final String? currentUrl = widget.pdfUrl ?? widget.pdfModel?.url;
    if (currentUrl != null && currentUrl.isNotEmpty) {
      if (kDebugMode) {
        print('🧪 اختبار الرابط الحالي: $currentUrl');
      }
      final result = await PDFUrlTester.testPdfUrl(currentUrl);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'نتيجة الاختبار: ${result['success'] ? 'نجح' : 'فشل'}\n'
              'السبب: ${result['error'] ?? 'غير محدد'}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: result['success'] ? Colors.green : Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  /// عرض رسالة خطأ
  Widget _buildErrorView(String message) {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error_outline, size: 80, color: Colors.red[400]),
        const SizedBox(height: 24),
        Text(
          'فشل في تحميل الملف',
          style: GoogleFonts.cairo(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.shade200),
          ),
          child: Column(
            children: [
              Text(
                message,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.red.shade800,
                ),
                textAlign: TextAlign.center,
              ),
              if (pdfUrl != null) ...[
                const SizedBox(height: 12),
                Text(
                  'الرابط: ${pdfUrl.length > 50 ? '${pdfUrl.substring(0, 50)}...' : pdfUrl}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton.icon(
              onPressed: _retryDownload,
              icon: const Icon(Icons.refresh, color: Colors.white),
              label: Text(
                'إعادة المحاولة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 4,
              ),
            ),
            const SizedBox(width: 16),
            OutlinedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(Icons.arrow_back, color: Colors.grey[600]),
              label: Text(
                'العودة',
                style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey[600]),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: BorderSide(color: Colors.grey[400]!),
              ),
            ),
          ],
        ),
        if (kDebugMode) ...[
          const SizedBox(height: 16),
          TextButton.icon(
            onPressed: _testSpecificUrl,
            icon: Icon(Icons.bug_report, color: Colors.orange[600]),
            label: Text(
              'اختبار تشخيصي',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.orange[600]),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.pdfFileName?.replaceAll('.pdf', '') ??
                      widget.subject.arabicName,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  widget.category != null
                      ? '${widget.category} • ${widget.subject.arabicName}'
                      : 'عارض PDF',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            icon: Icon(_showControls ? Icons.visibility_off : Icons.visibility),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      top: 20,
      right: 20,
      child: FadeTransition(
        opacity: _fabAnimation,
        child: Column(
          children: [
            // إعادة تحميل
            FloatingActionButton.small(
              onPressed: _hasError ? _retryDownload : null,
              backgroundColor: Colors.white,
              foregroundColor: _hasError ? Colors.red : Colors.grey,
              child: const Icon(Icons.refresh),
            ),
            const SizedBox(height: 8),

            // الصفحة السابقة
            FloatingActionButton.small(
              onPressed:
                  _currentPage > 1 && _pdfViewController != null
                      ? () async {
                        await _pdfViewController!.setPage(_currentPage - 2);
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_up),
            ),
            const SizedBox(height: 8),

            // الصفحة التالية
            FloatingActionButton.small(
              onPressed:
                  _currentPage < _totalPages && _pdfViewController != null
                      ? () async {
                        await _pdfViewController!.setPage(_currentPage);
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_down),
            ),
            const SizedBox(height: 8),

            // الانتقال إلى صفحة محددة
            FloatingActionButton.small(
              onPressed: _totalPages > 0 ? _showPageSelector : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.format_list_numbered),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض منتقي الصفحات
  void _showPageSelector() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'الانتقال إلى صفحة',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'الصفحة الحالية: $_currentPage من $_totalPages',
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: _currentPage.toDouble(),
                  min: 1,
                  max: _totalPages.toDouble(),
                  divisions: _totalPages - 1,
                  label: _currentPage.toString(),
                  onChanged: (value) async {
                    final page = value.toInt();
                    final navigator = Navigator.of(context);
                    if (_pdfViewController != null) {
                      await _pdfViewController!.setPage(page - 1);
                    }
                    if (mounted) {
                      navigator.pop();
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  Widget _buildBottomInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الصفحة $_currentPage من $_totalPages',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          Row(
            children: [
              Icon(
                Icons.picture_as_pdf,
                size: 16,
                color: AppTheme.textSecondaryColor,
              ),
              const SizedBox(width: 4),
              Text(
                'PDF محلي',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// تحميل الملف
  void _downloadFile(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن تحميل الملف', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الملف: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض الرابط المباشر
  void _showDirectUrl(String url) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'الرابط المباشر',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'يمكنك نسخ هذا الرابط واستخدامه مباشرة:',
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: SelectableText(
                    url,
                    style: GoogleFonts.cairo(fontSize: 12),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('إغلاق', style: GoogleFonts.cairo()),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _openInNewTab(url);
                },
                child: Text('فتح الرابط', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }
}
